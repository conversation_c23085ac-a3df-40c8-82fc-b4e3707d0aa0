// 存档功能测试脚本
// 在浏览器控制台中运行此脚本来测试存档功能

export function testSaveStateManager() {
  console.log('开始测试存档管理器...');
  
  // 测试数据
  const testGameId = 'test-game';
  const mockSaveState = {
    cpu: { registers: { A: 0x10, X: 0x20, Y: 0x30 } },
    ppu: { scanline: 240, cycle: 341 },
    papu: { channels: [] },
    mmap: { memory: new Array(0x10000).fill(0) },
    rom: { data: 'test-rom-data' }
  };

  try {
    // 动态导入SaveStateManager
    import('../utils/SaveStateManager').then(({ SaveStateManager }) => {
      const manager = new SaveStateManager(testGameId);
      
      console.log('1. 测试初始状态...');
      console.log('初始存档数量:', manager.getStateCount());
      console.log('是否有存档:', manager.hasStates());
      
      console.log('2. 测试保存存档...');
      const slot1 = manager.saveState(mockSaveState, 'data:image/png;base64,test-screenshot');
      console.log('保存的存档:', slot1);
      
      console.log('3. 测试获取存档列表...');
      const states = manager.getSaveStates();
      console.log('存档列表:', states);
      
      console.log('4. 测试加载存档...');
      const loadedSlot = manager.loadState(slot1.id);
      console.log('加载的存档:', loadedSlot);
      
      console.log('5. 测试多个存档...');
      const slot2 = manager.saveState(mockSaveState);
      const slot3 = manager.saveState(mockSaveState);
      const slot4 = manager.saveState(mockSaveState); // 这个应该会覆盖最旧的
      
      console.log('所有存档:', manager.getSaveStates());
      console.log('存档数量:', manager.getStateCount());
      
      console.log('6. 测试删除存档...');
      const deleteResult = manager.deleteState(slot2.id);
      console.log('删除结果:', deleteResult);
      console.log('删除后的存档:', manager.getSaveStates());
      
      console.log('7. 测试清空所有存档...');
      manager.clearAllStates();
      console.log('清空后的存档数量:', manager.getStateCount());
      
      console.log('✅ 存档管理器测试完成！');
    });
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 测试工具函数
export function testUtilityFunctions() {
  console.log('测试工具函数...');
  
  import('../utils/SaveStateManager').then(({ SaveStateManager }) => {
    // 测试游戏ID生成
    const gameId1 = SaveStateManager.generateGameId('https://assets.playunb.com/games/super-mario-bros.nes');
    const gameId2 = SaveStateManager.generateGameId('https://assets.playunb.com/games/contra.nes');
    console.log('游戏ID 1:', gameId1);
    console.log('游戏ID 2:', gameId2);
    
    // 测试时间格式化
    const timestamp = Date.now();
    const formatted = SaveStateManager.formatTimestamp(timestamp);
    console.log('格式化时间:', formatted);
    
    // 测试大小格式化
    const sizes = [0, 1024, 1048576, 1073741824];
    sizes.forEach(size => {
      console.log(`${size} bytes =`, SaveStateManager.formatSize(size));
    });
    
    console.log('✅ 工具函数测试完成！');
  });
}

// 在浏览器控制台中运行:
// import('./test/savestate-test.js').then(m => m.testSaveStateManager())
// import('./test/savestate-test.js').then(m => m.testUtilityFunctions())

console.log('存档功能测试脚本已加载');
console.log('使用方法:');
console.log('1. testSaveStateManager() - 测试存档管理器');
console.log('2. testUtilityFunctions() - 测试工具函数');
