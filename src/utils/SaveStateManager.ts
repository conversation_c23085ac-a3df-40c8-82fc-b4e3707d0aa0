import { SaveState } from 'jsnes';

export interface SaveSlot {
  id: number;
  timestamp: number;
  gameId: string;
  data: SaveState;
  screenshot?: string; // Base64 encoded screenshot
}

export interface SaveStateData {
  slots: SaveSlot[];
  nextSlotId: number;
}

export class SaveStateManager {
  private static readonly MAX_SLOTS = 3;
  private static readonly STORAGE_KEY_PREFIX = 'nes_save_';
  
  private gameId: string;
  private storageKey: string;

  constructor(gameId: string) {
    this.gameId = gameId;
    this.storageKey = `${SaveStateManager.STORAGE_KEY_PREFIX}${gameId}`;
  }

  /**
   * 获取当前游戏的所有存档
   */
  getSaveStates(): SaveSlot[] {
    try {
      const data = localStorage.getItem(this.storageKey);
      if (!data) return [];
      
      const saveData: SaveStateData = JSON.parse(data);
      return saveData.slots.sort((a, b) => b.timestamp - a.timestamp);
    } catch (error) {
      console.error('Failed to load save states:', error);
      return [];
    }
  }

  /**
   * 保存游戏状态
   */
  saveState(nesState: SaveState, screenshot?: string): SaveSlot {
    try {
      const existingData = this.loadSaveData();
      const timestamp = Date.now();
      
      const newSlot: SaveSlot = {
        id: existingData.nextSlotId,
        timestamp,
        gameId: this.gameId,
        data: nesState,
        screenshot
      };

      // 添加新存档
      existingData.slots.push(newSlot);
      existingData.nextSlotId++;

      // 如果超过最大存档数，删除最旧的
      if (existingData.slots.length > SaveStateManager.MAX_SLOTS) {
        existingData.slots.sort((a, b) => a.timestamp - b.timestamp);
        existingData.slots.shift(); // 删除最旧的
      }

      // 保存到localStorage
      localStorage.setItem(this.storageKey, JSON.stringify(existingData));
      
      console.log(`Save state created: Slot ${newSlot.id} at ${new Date(timestamp).toLocaleString()}`);
      return newSlot;
    } catch (error) {
      console.error('Failed to save state:', error);
      throw new Error('Failed to save game state');
    }
  }

  /**
   * 加载指定存档
   */
  loadState(slotId: number): SaveSlot | null {
    try {
      const slots = this.getSaveStates();
      const slot = slots.find(s => s.id === slotId);
      
      if (!slot) {
        console.warn(`Save slot ${slotId} not found`);
        return null;
      }
      
      console.log(`Loading save state: Slot ${slotId} from ${new Date(slot.timestamp).toLocaleString()}`);
      return slot;
    } catch (error) {
      console.error('Failed to load state:', error);
      return null;
    }
  }

  /**
   * 删除指定存档
   */
  deleteState(slotId: number): boolean {
    try {
      const existingData = this.loadSaveData();
      const initialLength = existingData.slots.length;
      
      existingData.slots = existingData.slots.filter(slot => slot.id !== slotId);
      
      if (existingData.slots.length === initialLength) {
        console.warn(`Save slot ${slotId} not found for deletion`);
        return false;
      }
      
      localStorage.setItem(this.storageKey, JSON.stringify(existingData));
      console.log(`Save slot ${slotId} deleted`);
      return true;
    } catch (error) {
      console.error('Failed to delete state:', error);
      return false;
    }
  }

  /**
   * 清空所有存档
   */
  clearAllStates(): void {
    try {
      localStorage.removeItem(this.storageKey);
      console.log(`All save states cleared for game: ${this.gameId}`);
    } catch (error) {
      console.error('Failed to clear save states:', error);
    }
  }

  /**
   * 获取最新的存档
   */
  getLatestState(): SaveSlot | null {
    const slots = this.getSaveStates();
    return slots.length > 0 ? slots[0] : null;
  }

  /**
   * 检查是否有存档
   */
  hasStates(): boolean {
    return this.getSaveStates().length > 0;
  }

  /**
   * 获取存档数量
   */
  getStateCount(): number {
    return this.getSaveStates().length;
  }

  /**
   * 获取存档大小（字节）
   */
  getStorageSize(): number {
    try {
      const data = localStorage.getItem(this.storageKey);
      return data ? new Blob([data]).size : 0;
    } catch (error) {
      console.error('Failed to calculate storage size:', error);
      return 0;
    }
  }

  /**
   * 从localStorage加载存档数据
   */
  private loadSaveData(): SaveStateData {
    try {
      const data = localStorage.getItem(this.storageKey);
      if (!data) {
        return { slots: [], nextSlotId: 1 };
      }
      
      return JSON.parse(data);
    } catch (error) {
      console.error('Failed to parse save data:', error);
      return { slots: [], nextSlotId: 1 };
    }
  }

  /**
   * 生成游戏ID（基于ROM URL）
   */
  static generateGameId(romUrl: string): string {
    // 从ROM URL中提取游戏名称
    const urlParts = romUrl.split('/');
    const filename = urlParts[urlParts.length - 1];
    const gameName = filename.replace(/\.(nes|rom)$/i, '');
    return gameName.toLowerCase().replace(/[^a-z0-9]/g, '_');
  }

  /**
   * 格式化存档时间
   */
  static formatTimestamp(timestamp: number): string {
    const date = new Date(timestamp);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  }

  /**
   * 格式化存档大小
   */
  static formatSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
