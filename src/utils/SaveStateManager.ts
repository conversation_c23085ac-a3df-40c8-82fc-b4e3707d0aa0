import { SaveState } from 'jsnes';

export interface SaveSlot {
  id: number;
  timestamp: number;
  gameId: string;
  data: SaveState;
  screenshot?: string; // Base64 encoded screenshot
}

export interface SaveStateData {
  slots: SaveSlot[];
  nextSlotId: number;
}

export class SaveStateManager {
  private static readonly MAX_SLOTS = 3;
  private static readonly STORAGE_KEY_PREFIX = 'nes_save_';
  
  private gameId: string;
  private storageKey: string;

  constructor(gameId: string) {
    this.gameId = gameId;
    this.storageKey = `${SaveStateManager.STORAGE_KEY_PREFIX}${gameId}`;
  }

  /**
   * 获取当前游戏的所有存档
   */
  getSaveStates(): SaveSlot[] {
    try {
      const data = localStorage.getItem(this.storageKey);
      if (!data) return [];
      
      const saveData: SaveStateData = JSON.parse(data);
      return saveData.slots.sort((a, b) => b.timestamp - a.timestamp);
    } catch (error) {
      console.error('Failed to load save states:', error);
      return [];
    }
  }

  /**
   * 保存游戏状态
   */
  saveState(nesState: SaveState, screenshot?: string): SaveSlot {
    try {
      const existingData = this.loadSaveData();
      const timestamp = Date.now();

      // 不保存截图以节省空间 - NES状态数据已经很大了
      const newSlot: SaveSlot = {
        id: existingData.nextSlotId,
        timestamp,
        gameId: this.gameId,
        data: nesState,
        // screenshot: undefined // 暂时不保存截图
      };

      // 添加新存档
      existingData.slots.push(newSlot);
      existingData.nextSlotId++;

      // 如果超过最大存档数，删除最旧的
      if (existingData.slots.length > SaveStateManager.MAX_SLOTS) {
        existingData.slots.sort((a, b) => a.timestamp - b.timestamp);
        existingData.slots.shift(); // 删除最旧的
      }

      // 尝试保存到localStorage，处理配额超出错误
      try {
        const dataToSave = JSON.stringify(existingData);
        console.log(`Attempting to save ${this.formatSize(new Blob([dataToSave]).size)} of data`);
        localStorage.setItem(this.storageKey, dataToSave);
      } catch (quotaError) {
        if (quotaError instanceof Error && quotaError.name === 'QuotaExceededError') {
          console.warn('Storage quota exceeded, trying to free up space...');

          // 尝试删除最旧的存档并重试
          if (existingData.slots.length > 1) {
            existingData.slots.sort((a, b) => a.timestamp - b.timestamp);
            existingData.slots.shift(); // 删除最旧的

            const retryData = JSON.stringify(existingData);
            console.log(`Retrying with ${this.formatSize(new Blob([retryData]).size)} of data`);
            localStorage.setItem(this.storageKey, retryData);
          } else {
            // 如果只有一个存档还是太大，清空所有存档
            console.warn('Single save state too large, clearing all saves');
            this.clearAllStates();
            throw new Error('Save state too large for storage. All saves have been cleared.');
          }
        } else {
          throw quotaError;
        }
      }

      console.log(`Save state created: Slot ${newSlot.id} at ${new Date(timestamp).toLocaleString()}`);
      return newSlot;
    } catch (error) {
      console.error('Failed to save state:', error);
      if (error instanceof Error) {
        throw new Error(`Failed to save game state: ${error.message}`);
      } else {
        throw new Error('Failed to save game state: Unknown error');
      }
    }
  }

  /**
   * 加载指定存档
   */
  loadState(slotId: number): SaveSlot | null {
    try {
      const slots = this.getSaveStates();
      const slot = slots.find(s => s.id === slotId);
      
      if (!slot) {
        console.warn(`Save slot ${slotId} not found`);
        return null;
      }
      
      console.log(`Loading save state: Slot ${slotId} from ${new Date(slot.timestamp).toLocaleString()}`);
      return slot;
    } catch (error) {
      console.error('Failed to load state:', error);
      return null;
    }
  }

  /**
   * 删除指定存档
   */
  deleteState(slotId: number): boolean {
    try {
      const existingData = this.loadSaveData();
      const initialLength = existingData.slots.length;
      
      existingData.slots = existingData.slots.filter(slot => slot.id !== slotId);
      
      if (existingData.slots.length === initialLength) {
        console.warn(`Save slot ${slotId} not found for deletion`);
        return false;
      }
      
      localStorage.setItem(this.storageKey, JSON.stringify(existingData));
      console.log(`Save slot ${slotId} deleted`);
      return true;
    } catch (error) {
      console.error('Failed to delete state:', error);
      return false;
    }
  }

  /**
   * 清空所有存档
   */
  clearAllStates(): void {
    try {
      localStorage.removeItem(this.storageKey);
      console.log(`All save states cleared for game: ${this.gameId}`);
    } catch (error) {
      console.error('Failed to clear save states:', error);
    }
  }

  /**
   * 获取最新的存档
   */
  getLatestState(): SaveSlot | null {
    const slots = this.getSaveStates();
    return slots.length > 0 ? slots[0] : null;
  }

  /**
   * 检查是否有存档
   */
  hasStates(): boolean {
    return this.getSaveStates().length > 0;
  }

  /**
   * 获取存档数量
   */
  getStateCount(): number {
    return this.getSaveStates().length;
  }

  /**
   * 获取存档大小（字节）
   */
  getStorageSize(): number {
    try {
      const data = localStorage.getItem(this.storageKey);
      return data ? new Blob([data]).size : 0;
    } catch (error) {
      console.error('Failed to calculate storage size:', error);
      return 0;
    }
  }

  /**
   * 从localStorage加载存档数据
   */
  private loadSaveData(): SaveStateData {
    try {
      const data = localStorage.getItem(this.storageKey);
      if (!data) {
        return { slots: [], nextSlotId: 1 };
      }
      
      return JSON.parse(data);
    } catch (error) {
      console.error('Failed to parse save data:', error);
      return { slots: [], nextSlotId: 1 };
    }
  }

  /**
   * 生成游戏ID（基于ROM URL）
   */
  static generateGameId(romUrl: string): string {
    // 从ROM URL中提取游戏名称
    const urlParts = romUrl.split('/');
    const filename = urlParts[urlParts.length - 1];
    const gameName = filename.replace(/\.(nes|rom)$/i, '');
    return gameName.toLowerCase().replace(/[^a-z0-9]/g, '_');
  }

  /**
   * 格式化存档时间
   */
  static formatTimestamp(timestamp: number): string {
    const date = new Date(timestamp);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  }

  /**
   * 格式化存档大小
   */
  static formatSize(bytes: number): string {
    if (bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 检查localStorage可用空间
   */
  static checkStorageQuota(): { used: number; available: number; total: number } {
    let used = 0;
    let total = 5 * 1024 * 1024; // 假设5MB限制（实际可能更大）

    try {
      // 计算已使用空间
      for (let key in localStorage) {
        if (localStorage.hasOwnProperty(key)) {
          used += localStorage[key].length + key.length;
        }
      }

      // 尝试估算总可用空间
      try {
        const testKey = 'storage-test';
        const testData = 'x'.repeat(1024); // 1KB test data
        let testSize = 0;

        while (testSize < 10 * 1024 * 1024) { // 最多测试10MB
          try {
            localStorage.setItem(testKey, 'x'.repeat(testSize));
            testSize += 1024;
          } catch (e) {
            localStorage.removeItem(testKey);
            total = used + testSize;
            break;
          }
        }
        localStorage.removeItem(testKey);
      } catch (e) {
        // 如果测试失败，使用默认值
      }
    } catch (e) {
      console.warn('Failed to check storage quota:', e);
    }

    return {
      used,
      total,
      available: total - used
    };
  }

  /**
   * 获取存储使用情况
   */
  getStorageInfo(): string {
    const quota = SaveStateManager.checkStorageQuota();
    const gameStorage = this.getStorageSize();

    return `Game saves: ${SaveStateManager.formatSize(gameStorage)} | ` +
           `Total used: ${SaveStateManager.formatSize(quota.used)} / ${SaveStateManager.formatSize(quota.total)} ` +
           `(${Math.round((quota.used / quota.total) * 100)}%)`;
  }
}
