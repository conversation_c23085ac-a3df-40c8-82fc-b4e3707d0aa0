import { SaveState } from 'jsnes';

export interface SaveSlot {
  id: number;
  timestamp: number;
  gameId: string;
  data: SaveState;
  screenshot?: string; // Base64 encoded screenshot
}

export interface SaveStateData {
  slots: SaveSlot[];
  nextSlotId: number;
}

export class SaveStateManager {
  private static readonly MAX_SLOTS = 3;
  private static readonly STORAGE_KEY_PREFIX = 'nes_save_';
  
  private gameId: string;
  private storageKey: string;

  constructor(gameId: string) {
    this.gameId = gameId;
    this.storageKey = `${SaveStateManager.STORAGE_KEY_PREFIX}${gameId}`;

    // 初始化时清理无效的存储数据
    SaveStateManager.cleanupStorage();
  }

  /**
   * 获取当前游戏的所有存档
   */
  getSaveStates(): SaveSlot[] {
    try {
      const data = localStorage.getItem(this.storageKey);
      if (!data) return [];
      
      const saveData: SaveStateData = JSON.parse(data);
      return saveData.slots.sort((a, b) => b.timestamp - a.timestamp);
    } catch (error) {
      console.error('Failed to load save states:', error);
      return [];
    }
  }

  /**
   * 优化NES状态数据，移除不必要的信息
   */
  private optimizeNESState(nesState: SaveState): SaveState {
    // 创建状态的深拷贝
    const optimized = JSON.parse(JSON.stringify(nesState));

    // 移除可能的冗余数据
    // 注意：这里需要根据jsnes的实际状态结构来优化
    // 暂时保持原样，但添加日志来分析
    console.log('Original state structure:', Object.keys(optimized));

    return optimized;
  }

  /**
   * 保存游戏状态
   */
  saveState(nesState: SaveState, screenshot?: string): SaveSlot {
    try {
      const existingData = this.loadSaveData();
      const timestamp = Date.now();

      // 优化状态数据
      const optimizedState = this.optimizeNESState(nesState);

      // 不保存截图以节省空间 - NES状态数据已经很大了
      const newSlot: SaveSlot = {
        id: existingData.nextSlotId,
        timestamp,
        gameId: this.gameId,
        data: optimizedState,
        // screenshot: undefined // 暂时不保存截图
      };

      // 检查是否与最近的存档相同（避免重复保存）
      const latestSlot = existingData.slots.length > 0 ?
        existingData.slots.sort((a, b) => b.timestamp - a.timestamp)[0] : null;

      if (latestSlot) {
        const latestStateString = JSON.stringify(latestSlot.data);
        const newStateString = JSON.stringify(newSlot.data);

        if (latestStateString === newStateString) {
          console.log('State is identical to latest save, skipping...');
          return latestSlot; // 返回现有的存档
        }
      }

      // 添加新存档
      existingData.slots.push(newSlot);
      existingData.nextSlotId++;

      // 如果超过最大存档数，删除最旧的
      if (existingData.slots.length > SaveStateManager.MAX_SLOTS) {
        existingData.slots.sort((a, b) => a.timestamp - b.timestamp);
        existingData.slots.shift(); // 删除最旧的
      }

      // 分析数据大小
      const dataToSave = JSON.stringify(existingData);
      const dataSize = new Blob([dataToSave]).size;
      console.log(`Attempting to save ${SaveStateManager.formatSize(dataSize)} of data`);
      console.log(`Save slots count: ${existingData.slots.length}`);

      // 检查单个存档的大小
      const singleSlotSize = new Blob([JSON.stringify(newSlot)]).size;
      console.log(`Single save slot size: ${SaveStateManager.formatSize(singleSlotSize)}`);

      // 尝试保存到localStorage，处理配额超出错误
      try {
        localStorage.setItem(this.storageKey, dataToSave);
      } catch (quotaError) {
        if (quotaError instanceof Error && quotaError.name === 'QuotaExceededError') {
          console.warn('Storage quota exceeded, trying to free up space...');

          // 尝试删除最旧的存档并重试
          if (existingData.slots.length > 1) {
            existingData.slots.sort((a, b) => a.timestamp - b.timestamp);
            existingData.slots.shift(); // 删除最旧的

            const retryData = JSON.stringify(existingData);
            const retrySize = new Blob([retryData]).size;
            console.log(`Retrying with ${SaveStateManager.formatSize(retrySize)} of data`);
            localStorage.setItem(this.storageKey, retryData);
          } else {
            // 如果只有一个存档还是太大，清空所有存档
            console.warn('Single save state too large, clearing all saves');
            this.clearAllStates();
            throw new Error('Save state too large for storage. All saves have been cleared.');
          }
        } else {
          throw quotaError;
        }
      }

      console.log(`Save state created: Slot ${newSlot.id} at ${new Date(timestamp).toLocaleString()}`);
      return newSlot;
    } catch (error) {
      console.error('Failed to save state:', error);
      if (error instanceof Error) {
        throw new Error(`Failed to save game state: ${error.message}`);
      } else {
        throw new Error('Failed to save game state: Unknown error');
      }
    }
  }

  /**
   * 加载指定存档
   */
  loadState(slotId: number): SaveSlot | null {
    try {
      const slots = this.getSaveStates();
      const slot = slots.find(s => s.id === slotId);
      
      if (!slot) {
        console.warn(`Save slot ${slotId} not found`);
        return null;
      }
      
      console.log(`Loading save state: Slot ${slotId} from ${new Date(slot.timestamp).toLocaleString()}`);
      return slot;
    } catch (error) {
      console.error('Failed to load state:', error);
      return null;
    }
  }

  /**
   * 删除指定存档
   */
  deleteState(slotId: number): boolean {
    try {
      const existingData = this.loadSaveData();
      const initialLength = existingData.slots.length;
      
      existingData.slots = existingData.slots.filter(slot => slot.id !== slotId);
      
      if (existingData.slots.length === initialLength) {
        console.warn(`Save slot ${slotId} not found for deletion`);
        return false;
      }
      
      localStorage.setItem(this.storageKey, JSON.stringify(existingData));
      console.log(`Save slot ${slotId} deleted`);
      return true;
    } catch (error) {
      console.error('Failed to delete state:', error);
      return false;
    }
  }

  /**
   * 清空所有存档
   */
  clearAllStates(): void {
    try {
      localStorage.removeItem(this.storageKey);
      console.log(`All save states cleared for game: ${this.gameId}`);
    } catch (error) {
      console.error('Failed to clear save states:', error);
    }
  }

  /**
   * 获取最新的存档
   */
  getLatestState(): SaveSlot | null {
    const slots = this.getSaveStates();
    return slots.length > 0 ? slots[0] : null;
  }

  /**
   * 检查是否有存档
   */
  hasStates(): boolean {
    return this.getSaveStates().length > 0;
  }

  /**
   * 获取存档数量
   */
  getStateCount(): number {
    return this.getSaveStates().length;
  }

  /**
   * 获取存档大小（字节）
   */
  getStorageSize(): number {
    try {
      const data = localStorage.getItem(this.storageKey);
      return data ? new Blob([data]).size : 0;
    } catch (error) {
      console.error('Failed to calculate storage size:', error);
      return 0;
    }
  }

  /**
   * 从localStorage加载存档数据
   */
  private loadSaveData(): SaveStateData {
    try {
      const data = localStorage.getItem(this.storageKey);
      if (!data) {
        return { slots: [], nextSlotId: 1 };
      }
      
      return JSON.parse(data);
    } catch (error) {
      console.error('Failed to parse save data:', error);
      return { slots: [], nextSlotId: 1 };
    }
  }

  /**
   * 生成游戏ID（基于ROM URL）
   */
  static generateGameId(romUrl: string): string {
    // 从ROM URL中提取游戏名称
    const urlParts = romUrl.split('/');
    const filename = urlParts[urlParts.length - 1];
    const gameName = filename.replace(/\.(nes|rom)$/i, '');
    return gameName.toLowerCase().replace(/[^a-z0-9]/g, '_');
  }

  /**
   * 格式化存档时间
   */
  static formatTimestamp(timestamp: number): string {
    const date = new Date(timestamp);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  }

  /**
   * 格式化存档大小
   */
  static formatSize(bytes: number): string {
    if (bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 检查localStorage可用空间
   */
  static checkStorageQuota(): { used: number; available: number; total: number } {
    let used = 0;
    let total = 5 * 1024 * 1024; // 假设5MB限制（实际可能更大）

    try {
      // 计算已使用空间
      for (let key in localStorage) {
        if (localStorage.hasOwnProperty(key)) {
          used += localStorage[key].length + key.length;
        }
      }

      // 尝试估算总可用空间
      try {
        const testKey = 'storage-test';
        const testData = 'x'.repeat(1024); // 1KB test data
        let testSize = 0;

        while (testSize < 10 * 1024 * 1024) { // 最多测试10MB
          try {
            localStorage.setItem(testKey, 'x'.repeat(testSize));
            testSize += 1024;
          } catch (e) {
            localStorage.removeItem(testKey);
            total = used + testSize;
            break;
          }
        }
        localStorage.removeItem(testKey);
      } catch (e) {
        // 如果测试失败，使用默认值
      }
    } catch (e) {
      console.warn('Failed to check storage quota:', e);
    }

    return {
      used,
      total,
      available: total - used
    };
  }

  /**
   * 获取存储使用情况
   */
  getStorageInfo(): string {
    const quota = SaveStateManager.checkStorageQuota();
    const gameStorage = this.getStorageSize();

    return `Game saves: ${SaveStateManager.formatSize(gameStorage)} | ` +
           `Total used: ${SaveStateManager.formatSize(quota.used)} / ${SaveStateManager.formatSize(quota.total)} ` +
           `(${Math.round((quota.used / quota.total) * 100)}%)`;
  }

  /**
   * 清理localStorage中的无效或过大的数据
   */
  static cleanupStorage(): void {
    try {
      const keysToRemove: string[] = [];

      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (!key) continue;

        try {
          const value = localStorage.getItem(key);
          if (!value) continue;

          const size = new Blob([value]).size;

          // 如果单个项目超过1MB，标记为清理
          if (size > 1024 * 1024) {
            console.warn(`Large localStorage item found: ${key} (${SaveStateManager.formatSize(size)})`);

            // 如果是我们的存档数据，尝试验证其有效性
            if (key.startsWith(SaveStateManager.STORAGE_KEY_PREFIX)) {
              try {
                const data = JSON.parse(value);
                if (!data.slots || !Array.isArray(data.slots)) {
                  console.warn(`Invalid save data structure for ${key}, marking for removal`);
                  keysToRemove.push(key);
                }
              } catch (e) {
                console.warn(`Corrupted save data for ${key}, marking for removal`);
                keysToRemove.push(key);
              }
            }
          }
        } catch (e) {
          console.warn(`Error checking localStorage item ${key}:`, e);
        }
      }

      // 移除标记的项目
      keysToRemove.forEach(key => {
        console.log(`Removing large/invalid localStorage item: ${key}`);
        localStorage.removeItem(key);
      });

      if (keysToRemove.length > 0) {
        console.log(`Cleaned up ${keysToRemove.length} localStorage items`);
      }
    } catch (error) {
      console.error('Failed to cleanup storage:', error);
    }
  }
}
