'use client';

import { useState, useEffect, useRef } from 'react';
import { Game } from '@/types/Game';
import { getGameContainerStyle, getGameDisplayInfo } from '@/utils/gameLayout';
import GameControls from '@/components/GameControls';
import GameContainer from '@/components/GameContainer';
import GameInstructions from '@/components/GameInstructions';
import SaveStateControls from '@/components/SaveStateControls';
import NESController from '@/components/controllers/NESController';
import GBAController from '@/components/controllers/GBAController';
import VirtualGamepadToggle from '@/components/VirtualGamepadToggle';
import { ControllerProvider } from '@/context/ControllerContext';
import Emulator from '@/components/nes/Emulator';

interface GamePageClientProps {
  game: Game;
}

export default function GamePageClient({ game }: GamePageClientProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const nesEmulatorRef = useRef<Emulator>(null);
  const [isBrowserFullscreen, setIsBrowserFullscreen] = useState(false);

  const gameContainerStyle = getGameContainerStyle(game);
  const gameDisplayInfo = getGameDisplayInfo(game);

  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsBrowserFullscreen(!!document.fullscreenElement);
    };

    // Keyboard shortcuts for save states (NES only)
    const handleKeyDown = (e: KeyboardEvent) => {
      if (game.runner !== 'EMULATOR_NES' || !nesEmulatorRef.current) return;

      // Prevent triggering in input fields
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
        return;
      }

      if (e.ctrlKey || e.metaKey) {
        switch (e.key.toLowerCase()) {
          case 'w':
            e.preventDefault();
            nesEmulatorRef.current.saveState();
            break;
          case 'r':
            e.preventDefault();
            const states = nesEmulatorRef.current.getSaveStates();
            if (states.length > 0) {
              nesEmulatorRef.current.loadState(states[0].id);
            }
            break;
        }
      }
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [game.runner]);

  const handleBrowserFullscreen = async () => {
    if (!containerRef.current) return;

    try {
      if (document.fullscreenElement) {
        await document.exitFullscreen();
      } else {
        await containerRef.current.requestFullscreen();
      }
    } catch (error) {
      console.error('Fullscreen error:', error);
    }
  };

  return (
    <ControllerProvider>
      <div className={`${isBrowserFullscreen ? 'h-screen flex flex-col bg-black' : 'bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden'} relative`}>
        {/* Game Container - This will become the fullscreen element */}
        <div className={`flex justify-center items-center ${isBrowserFullscreen ? 'flex-1 p-0' : 'p-4 sm:p-6 bg-gray-100 dark:bg-gray-700'}`}>
          <div
            ref={containerRef}
            className={`relative bg-black overflow-hidden ${isBrowserFullscreen ? 'w-full h-full' : 'rounded-lg shadow-lg'}`}
            style={isBrowserFullscreen ? {} : gameContainerStyle}
          >
            <GameContainer game={game} nesEmulatorRef={nesEmulatorRef} />

            {/* Virtual Gamepad Toggle - Always inside the fullscreen container */}
            <VirtualGamepadToggle runner={game.runner} />

            {/* Game Controls - Show in fullscreen mode */}
            {isBrowserFullscreen && (
              <div className="absolute top-4 left-4 z-40">
                <GameControls
                  gameName={game.name}
                  runner={game.runner}
                  onBrowserFullscreen={handleBrowserFullscreen}
                  isBrowserFullscreen={isBrowserFullscreen}
                />
              </div>
            )}

            {/* Game Controllers - Always inside the fullscreen container */}
            <NESController runner={game.runner} />
            <GBAController runner={game.runner} />
          </div>
        </div>

        {/* Game Info and Controls */}
        {!isBrowserFullscreen && (
          <div className="p-4 sm:p-6">
            <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
              <div className="min-w-0 flex-1">
                <h2 className="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white">
                  {game.name}
                </h2>
                <p className="text-sm text-gray-600 dark:text-gray-300 mt-1 leading-relaxed">
                  {game.description || `Play ${game.name} free online. Unblocked game available anywhere!`}
                </p>
                <div className="flex flex-wrap gap-2 mt-2">
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                    {game.runner === 'UNITY' ? 'Unity WebGL' :
                      game.runner === 'IFRAME' ? 'HTML5 Game' :
                        game.runner === 'EMULATOR_NES' ? 'NES Classic' :
                          game.runner === 'EMULATOR_GBA' ? 'GBA Retro' :
                            game.runner === 'RUFFLE' ? 'Flash Game' :
                              'Browser Game'}
                  </span>
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                    {gameDisplayInfo.originalSize}
                  </span>
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                    {gameDisplayInfo.optimizedFor}
                  </span>
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200">
                    {gameDisplayInfo.displayType}
                  </span>
                </div>
              </div>

              <div className="flex-shrink-0">
                <GameControls
                  gameName={game.name}
                  runner={game.runner}
                  onBrowserFullscreen={handleBrowserFullscreen}
                  isBrowserFullscreen={isBrowserFullscreen}
                />
              </div>
            </div>

            {/* Save State Controls (NES only) */}
            {game.runner === 'EMULATOR_NES' && (
              <SaveStateControls
                onSave={() => {
                  console.log('onSave called, nesEmulatorRef.current:', nesEmulatorRef.current);
                  if (!nesEmulatorRef.current) {
                    console.error('NES emulator ref is null');
                    return null;
                  }
                  try {
                    const result = nesEmulatorRef.current.saveState();
                    console.log('saveState result:', result);
                    return result;
                  } catch (error) {
                    console.error('Error calling saveState:', error);
                    return null;
                  }
                }}
                onLoadLatest={() => {
                  if (!nesEmulatorRef.current) return false;
                  const states = nesEmulatorRef.current.getSaveStates() || [];
                  if (states.length > 0) {
                    return nesEmulatorRef.current.loadState(states[0].id) || false;
                  }
                  return false;
                }}
                onClearAll={() => nesEmulatorRef.current?.clearAllStates()}
                getSaveStates={() => {
                  if (!nesEmulatorRef.current) return [];
                  try {
                    return nesEmulatorRef.current.getSaveStates() || [];
                  } catch (error) {
                    console.error('Error getting save states:', error);
                    return [];
                  }
                }}
                onLoadSpecific={(slotId) => nesEmulatorRef.current?.loadState(slotId) || false}
                onDeleteSpecific={(slotId) => nesEmulatorRef.current?.deleteState(slotId) || false}
              />
            )}

            {/* Game Instructions */}
            <GameInstructions game={game} />
          </div>
        )}




      </div>
    </ControllerProvider>
  );
} 