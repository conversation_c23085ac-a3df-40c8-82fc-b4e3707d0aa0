'use client';

import { useRef, useState, useEffect } from 'react';
import { Game } from '@/types/Game';
import { calculateGameLayout, calculateFullscreenLayout } from '@/utils/gameLayout';
import UnityGameDirect from './UnityGameDirect';
import NESEmulator from './nes/Emulator';
import RuffleEmulator from './ruffle/RuffleEmulator';
import GBAEmulator from './gba/GBAEmulator';
import SaveStateUI from './nes/SaveStateUI';
import { EmulatorRef } from './nes/Emulator';

interface GameContainerProps {
  game: Game;
}

export default function GameContainer({ game }: GameContainerProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const nesEmulatorRef = useRef<EmulatorRef>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [fullscreenLayout, setFullscreenLayout] = useState<ReturnType<typeof calculateFullscreenLayout> | null>(null);
  const [showSaveStateUI, setShowSaveStateUI] = useState(false);
  const [hasStates, setHasStates] = useState(false);

  const gameLayout = calculateGameLayout(game);

  useEffect(() => {
    const handleFullscreenChange = () => {
      const isNowFullscreen = !!document.fullscreenElement;
      setIsFullscreen(isNowFullscreen);

      // 计算全屏布局
      if (isNowFullscreen) {
        // 使用setTimeout确保DOM已经更新
        setTimeout(() => {
          const layout = calculateFullscreenLayout(
            game,
            window.innerWidth,
            window.innerHeight
          );
          setFullscreenLayout(layout);
        }, 100);
      } else {
        setFullscreenLayout(null);
      }
    };

    // 监听窗口大小变化
    const handleResize = () => {
      if (isFullscreen) {
        const layout = calculateFullscreenLayout(
          game,
          window.innerWidth,
          window.innerHeight
        );
        setFullscreenLayout(layout);
      }
    };

    // 键盘快捷键处理（仅NES游戏）
    const handleKeyDown = (e: KeyboardEvent) => {
      if (game.runner !== 'EMULATOR_NES' || !nesEmulatorRef.current) return;

      // 防止在输入框中触发
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
        return;
      }

      switch (e.key) {
        case 'F5':
        case 'F6':
        case 'F7':
          e.preventDefault();
          nesEmulatorRef.current.saveState();
          break;
        case 'F9':
        case 'F10':
        case 'F11':
          e.preventDefault();
          const slotMap: { [key: string]: number } = { 'F9': 1, 'F10': 2, 'F11': 3 };
          const states = nesEmulatorRef.current.getSaveStates();
          const targetSlot = states.find(s => s.id === slotMap[e.key]);
          if (targetSlot) {
            nesEmulatorRef.current.loadState(targetSlot.id);
          }
          break;
        case 'F8':
          e.preventDefault();
          setShowSaveStateUI(true);
          break;
      }
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    window.addEventListener('resize', handleResize);
    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      window.removeEventListener('resize', handleResize);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [game, isFullscreen]);

  const handleFullscreen = async () => {
    if (!containerRef.current) return;

    try {
      if (document.fullscreenElement) {
        await document.exitFullscreen();
      } else {
        await containerRef.current.requestFullscreen();
      }
    } catch (error) {
      console.error('Fullscreen error:', error);
    }
  };

  const handleLoad = () => {
    setIsLoading(false);
  };

  const handleUnityLoadingChange = (loading: boolean) => {
    setIsLoading(loading);
  };

  const handleSaveStateChange = (hasStates: boolean) => {
    setHasStates(hasStates);
  };

  // 调试用：检查游戏类型
  useEffect(() => {
    console.log('GameContainer: Game runner:', game.runner);
    console.log('GameContainer: Is loading:', isLoading);
    console.log('GameContainer: Should show save button:', game.runner === 'EMULATOR_NES' && !isLoading);
  }, [game.runner, isLoading]);



  // 渲染游戏内容 - 始终保持相同的渲染路径，避免重新挂载
  const renderGameContent = () => {
    // 移除isFullscreen prop，避免因prop变化导致重新渲染
    if (game.runner === 'UNITY') {
      return (
        <UnityGameDirect
          game={game}
          onLoadingChange={handleUnityLoadingChange}
        />
      );
    } else if (game.runner === 'EMULATOR_NES') {
      return (
        <NESEmulator
          ref={nesEmulatorRef}
          romData={game.game}
          onLoadingChange={handleUnityLoadingChange}
          onSaveStateChange={handleSaveStateChange}
          paused={false}
        />
      );
    } else if (game.runner === 'EMULATOR_GBA') {
      return (
        <GBAEmulator
          romData={game.game}
          onLoadingChange={handleUnityLoadingChange}
          paused={false}
        />
      );
    } else if (game.runner === 'RUFFLE') {
      return (
        <RuffleEmulator
          game={game}
          onLoadingChange={handleUnityLoadingChange}
          paused={false}
        />
      );
    } else {
      return (
        <iframe
          src={game.game}
          title={game.name}
          className="w-full h-full border-0"
          allowFullScreen
          loading="lazy"
          onLoad={handleLoad}
          allow="autoplay; fullscreen; microphone; camera"
        />
      );
    }
  };

  return (
    <div className="relative w-full h-full">
      {/* Loading Overlay */}
      {isLoading && (
        <div className="absolute inset-0 bg-black bg-opacity-90 flex items-center justify-center z-10 rounded-lg">
          <div className="text-center text-white px-4">
            <div className="w-12 h-12 sm:w-16 sm:h-16 border-4 border-blue-500 border-t-transparent border-solid rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-base sm:text-lg font-semibold">Loading {game.name}...</p>
            <p className="text-xs sm:text-sm opacity-75 mt-2">
              Optimized for {gameLayout.width}×{gameLayout.height}
            </p>
          </div>
        </div>
      )}


      {/* Game Container */}
      <div
        ref={containerRef}
        className="w-full h-full bg-black relative overflow-hidden rounded-lg"
        style={{
          ...(isFullscreen && {
            borderRadius: 0,
            width: '100vw',
            height: '100vh',
          })
        }}
      >
        {/* 游戏内容容器 - 始终保持相同的DOM结构，避免重新挂载 */}
        <div
          className="absolute bg-black flex items-center justify-center transition-all duration-300 ease-in-out"
          style={{
            // 全屏模式下应用等比缩放，非全屏模式下填满容器
            ...(isFullscreen && fullscreenLayout ? {
              width: fullscreenLayout.width,
              height: fullscreenLayout.height,
              left: fullscreenLayout.left,
              top: fullscreenLayout.top,
            } : {
              width: '100%',
              height: '100%',
              left: 0,
              top: 0,
            })
          }}

        >
          {renderGameContent()}
        </div>
      </div>

      {/* Save State Button (NES only) */}
      {game.runner === 'EMULATOR_NES' && !isLoading && (
        <button
          onClick={() => {
            console.log('Save state button clicked');
            setShowSaveStateUI(true);
          }}
          className="absolute top-4 right-4 bg-black bg-opacity-50 hover:bg-opacity-75 text-white p-2 rounded-lg transition-all duration-200 z-10"
          title="存档管理 (F8)"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
          </svg>
          {hasStates && (
            <span className="absolute -top-1 -right-1 bg-green-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
              {nesEmulatorRef.current?.getSaveStates().length || 0}
            </span>
          )}
        </button>
      )}

      {/* Game Info Overlay (only when not fullscreen and not loading) */}
      {!isFullscreen && !isLoading && (
        <div className="absolute bottom-2 left-2 sm:bottom-4 sm:left-4 bg-black bg-opacity-50 text-white p-2 rounded-lg text-xs sm:text-sm opacity-0 hover:opacity-100 transition-opacity">
          <p className="font-semibold">{game.name}</p>
          <p className="text-xs opacity-75">
            {gameLayout.width}×{gameLayout.height} •
            {game.runner === 'UNITY' ? ' Unity WebGL' :
             game.runner === 'IFRAME' ? ' HTML5' :
             game.runner === 'EMULATOR_NES' ? ' NES Emulated' :
             game.runner === 'EMULATOR_GBA' ? ' GBA Emulated' :
             ' Emulated'}
          </p>
          {game.runner === 'EMULATOR_NES' && (
            <p className="text-xs opacity-75 mt-1">
              F5-F7: 保存 • F9-F11: 加载 • F8: 存档管理
            </p>
          )}
        </div>
      )}

      {/* Save State UI */}
      {game.runner === 'EMULATOR_NES' && (
        <SaveStateUI
          isVisible={showSaveStateUI}
          onClose={() => setShowSaveStateUI(false)}
          onSave={() => nesEmulatorRef.current?.saveState() || null}
          onLoad={(slotId) => nesEmulatorRef.current?.loadState(slotId) || false}
          onDelete={(slotId) => nesEmulatorRef.current?.deleteState(slotId) || false}
          onClearAll={() => nesEmulatorRef.current?.clearAllStates()}
          getSaveStates={() => nesEmulatorRef.current?.getSaveStates() || []}
        />
      )}
    </div>
  );
}