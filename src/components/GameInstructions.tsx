import { Game } from '@/types/Game';
import AdSense from '@/components/AdSense';

interface GameInstructionsProps {
  game: Game;
}

interface ControlMapping {
  key: string;
  action: string;
  icon?: string;
}

const NESControls: ControlMapping[] = [
  { key: '↑ ↓ ← → / WASD', action: 'D-Pad (Move)', icon: '🎮' },
  { key: 'X / K', action: 'A Button', icon: '🔴' },
  { key: 'Z / J', action: 'B Button', icon: '🔵' },
  { key: 'L', action: 'A+B Combo', icon: '🟡' },
  { key: 'I', action: 'A Rapid Fire', icon: '🔥' },
  { key: 'U', action: 'B Rapid Fire', icon: '🔥' },
  { key: 'O', action: 'A+B Rapid Fire', icon: '💥' },
  { key: 'Enter', action: 'Start', icon: '▶️' },
  { key: 'Ctrl', action: 'Select', icon: '⚙️' },
];

const GBAControls: ControlMapping[] = [
  { key: '↑ ↓ ← → / WASD', action: 'D-Pad (Move)', icon: '🎮' },
  { key: 'X / K / Space', action: 'A Button', icon: '🔴' },
  { key: 'Z / L', action: 'B Button', icon: '🔵' },
  { key: 'Q / I', action: 'L Shoulder', icon: '🅻' },
  { key: 'E / O', action: 'R Shoulder', icon: '🆁' },
  { key: 'Enter', action: 'Start', icon: '▶️' },
  { key: 'Shift', action: 'Select', icon: '⚙️' },
];

const DefaultControls: ControlMapping[] = [
  { key: 'Mouse', action: 'Point and click', icon: '🖱️' },
  { key: 'WASD', action: 'Move (if supported)', icon: '⌨️' },
  { key: 'Space', action: 'Action/Jump (if supported)', icon: '🚀' },
];

const FlashControls: ControlMapping[] = [
  { key: 'Mouse', action: 'Click, drag and interact', icon: '🖱️' },
  { key: 'Keyboard', action: 'Follow game instructions', icon: '⌨️' },
  { key: 'Arrow Keys', action: 'Move character (common)', icon: '🎮' },
  { key: 'Spacebar', action: 'Action/Jump (common)', icon: '🚀' },
];

function ControlItem({ control }: { control: ControlMapping }) {
  return (
    <div className="flex items-center gap-3 p-2 bg-white dark:bg-gray-600 rounded-lg border border-gray-200 dark:border-gray-500">
      {control.icon && (
        <span className="text-lg" role="img" aria-hidden="true">
          {control.icon}
        </span>
      )}
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2">
          <kbd className="px-2 py-1 text-xs font-mono bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded text-gray-800 dark:text-gray-200">
            {control.key}
          </kbd>
          <span className="text-sm text-gray-700 dark:text-gray-300">
            {control.action}
          </span>
        </div>
      </div>
    </div>
  );
}

export default function GameInstructions({ game }: GameInstructionsProps) {
  const getControlsForGame = (): ControlMapping[] => {
    switch (game.runner) {
      case 'EMULATOR_NES':
        return NESControls;
      case 'EMULATOR_GBA':
        return GBAControls;
      case 'RUFFLE':
        return FlashControls;
      case 'UNITY':
      case 'IFRAME':
      default:
        return DefaultControls;
    }
  };

  const getGameTypeDescription = (): string => {
    switch (game.runner) {
      case 'EMULATOR_NES':
        return 'Classic NES game controls - experience retro gaming at its finest!';
      case 'EMULATOR_GBA':
        return 'Game Boy Advance emulation - portable gaming perfection with enhanced controls!';
      case 'UNITY':
        return 'Unity WebGL game - may require clicking to activate and load properly.';
      case 'IFRAME':
        return 'HTML5 game - modern web-based gaming experience.';
      case 'RUFFLE':
        return 'Flash game emulated through Ruffle - classic Flash gaming preserved and modernized.';
      default:
        return 'Interactive game - use controls as indicated.';
    }
  };

  const getGameTypeIcon = (): string => {
    switch (game.runner) {
      case 'EMULATOR_NES':
        return '🕹️';
      case 'EMULATOR_GBA':
        return '🎮';
      case 'UNITY':
        return '🎮';
      case 'IFRAME':
        return '🌐';
      case 'RUFFLE':
        return '⚡';
      default:
        return '🎯';
    }
  };

  const controls = getControlsForGame();

  return (
    <div className="mt-4 sm:mt-6 p-4 sm:p-6 bg-gray-50 dark:bg-gray-700 rounded-lg">
      <div className="flex items-center gap-2 mb-4">
        <span className="text-xl" role="img" aria-hidden="true">
          {getGameTypeIcon()}
        </span>
        <h3 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white">
          Game Controls
        </h3>
      </div>
      
      <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
        {getGameTypeDescription()}
      </p>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
        {controls.map((control, index) => (
          <ControlItem key={index} control={control} />
        ))}
      </div>

      {game.runner === 'EMULATOR_NES' && (
        <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <span className="text-sm" role="img" aria-hidden="true">💡</span>
            <h4 className="text-sm font-medium text-blue-900 dark:text-blue-200">
              NES Gaming Tips
            </h4>
          </div>
          <ul className="text-xs sm:text-sm text-blue-800 dark:text-blue-300 space-y-1">
            <li>• Click anywhere on the game to enable audio</li>
            <li>• Use fullscreen for the best retro gaming experience</li>
            <li>• Some games may require both A and B buttons</li>
            <li>• Try different button combinations for special moves</li>
          </ul>
        </div>
      )}

      {game.runner === 'EMULATOR_GBA' && (
        <div className="mt-4 p-3 bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-800 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <span className="text-sm" role="img" aria-hidden="true">🎮</span>
            <h4 className="text-sm font-medium text-green-900 dark:text-green-200">
              GBA Gaming Tips
            </h4>
          </div>
          <ul className="text-xs sm:text-sm text-green-800 dark:text-green-300 space-y-1">
            <li>• Click anywhere on the game to enable audio</li>
            <li>• Use shoulder buttons (L/R) for enhanced gameplay</li>
            <li>• Try both keyboard layouts: arrows or WASD</li>
            <li>• Games may take a moment to load initially</li>
            <li>• Use fullscreen for the authentic portable experience</li>
          </ul>
        </div>
      )}

      {game.runner === 'UNITY' && (
        <div className="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-800 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <span className="text-sm" role="img" aria-hidden="true">⚠️</span>
            <h4 className="text-sm font-medium text-yellow-900 dark:text-yellow-200">
              Unity Game Notes
            </h4>
          </div>
          <ul className="text-xs sm:text-sm text-yellow-800 dark:text-yellow-300 space-y-1">
            <li>• If the game appears black, click on it to activate</li>
            <li>• May take a moment to load completely</li>
            <li>• Use fullscreen for optimal performance</li>
          </ul>
        </div>
      )}

      {game.runner === 'RUFFLE' && (
        <div className="mt-4 p-3 bg-orange-50 dark:bg-orange-900/30 border border-orange-200 dark:border-orange-800 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <span className="text-sm" role="img" aria-hidden="true">⚡</span>
            <h4 className="text-sm font-medium text-orange-900 dark:text-orange-200">
              Flash Game Tips
            </h4>
          </div>
          <ul className="text-xs sm:text-sm text-orange-800 dark:text-orange-300 space-y-1">
            <li>• Click anywhere on the game to enable audio and start playing</li>
            <li>• Flash games are emulated using Ruffle for modern browser compatibility</li>
            <li>• Controls vary by game - check in-game instructions or menus</li>
            <li>• Some advanced Flash features may not work perfectly</li>
            <li>• Right-click may show game-specific context menus</li>
          </ul>
        </div>
      )}

      <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-600">
        <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
          Having trouble? Try refreshing the page or using fullscreen mode for the best experience.
        </p>
      </div>

      {/* Advertisement */}
      <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-600">
        <div className="text-center">
          <AdSense
            adSlot="1234567890"
            adFormat="auto"
            style={{ display: 'block', minHeight: '100px' }}
            className="w-full"
          />
        </div>
      </div>
    </div>
  );
}