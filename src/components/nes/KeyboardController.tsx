import { Controller } from "jsnes";

// Mapping keyboard code to [controller, button, description]
const KEYS: { [key: number]: [number, number, string] } = {
  88: [1, Controller.BUTTON_A, "X"], // X (legacy)
  89: [1, Controller.BUTTON_B, "Y"], // Y (Central European keyboard, legacy)
  90: [1, Controller.BUTTON_B, "Z"], // Z (legacy)
  75: [1, Controller.BUTTON_A, "K"], // <PERSON> (A button)
  74: [1, Controller.BUTTON_B, "J"], // J (B button)
  17: [1, Controller.BUTTON_SELECT, "Right Ctrl"], // Right Ctrl
  13: [1, Controller.BUTTON_START, "Enter"], // Enter
  38: [1, Controller.BUTTON_UP, "Up"], // Up (legacy)
  40: [1, Controller.BUTTON_DOWN, "Down"], // Down (legacy)
  37: [1, Controller.BUTTON_LEFT, "Left"], // Left (legacy)
  39: [1, Controller.BUTTO<PERSON>_RIGHT, "Right"], // Right (legacy)
  87: [1, Controller.BUTTON_UP, "W"], // W (Up)
  83: [1, Controller.BUTTON_DOWN, "S"], // S (Down)
  65: [1, Controller.BUTTON_LEFT, "A"], // A (Left)
  68: [1, Controller.BUTTON_RIGHT, "D"], // D (Right)
  103: [2, Controller.BUTTON_A, "Num-7"], // Num-7
  105: [2, Controller.BUTTON_B, "Num-9"], // Num-9
  99: [2, Controller.BUTTON_SELECT, "Num-3"], // Num-3
  97: [2, Controller.BUTTON_START, "Num-1"], // Num-1
  104: [2, Controller.BUTTON_UP, "Num-8"], // Num-8
  98: [2, Controller.BUTTON_DOWN, "Num-2"], // Num-2
  100: [2, Controller.BUTTON_LEFT, "Num-4"], // Num-4
  102: [2, Controller.BUTTON_RIGHT, "Num-6"] // Num-6
};

interface KeyboardControllerOptions {
  onButtonDown: (controller: number, button: number) => void;
  onButtonUp: (controller: number, button: number) => void;
}

export default class KeyboardController {
  private onButtonDown: (controller: number, button: number) => void;
  private onButtonUp: (controller: number, button: number) => void;
  private keys: { [key: number]: [number, number, string] };
  private rapidFireIntervals: { [key: number]: NodeJS.Timeout } = {};
  private comboKeyStates: { [key: number]: boolean } = {};

  constructor(options: KeyboardControllerOptions) {
    this.onButtonDown = options.onButtonDown;
    this.onButtonUp = options.onButtonUp;
    this.keys = KEYS;
  }

  loadKeys = () => {
    var keys;
    try {
      const storedKeys = localStorage.getItem("keys");
      if (storedKeys) {
        keys = JSON.parse(storedKeys);
      }
    } catch (e) {
      console.log("Failed to get keys from localStorage.", e);
    }

    this.keys = keys || KEYS;
  };

  setKeys = (newKeys: { [key: number]: [number, number, string] }) => {
    try {
      localStorage.setItem("keys", JSON.stringify(newKeys));
      this.keys = newKeys;
    } catch (e) {
      console.log("Failed to set keys in localStorage");
    }
  };

  handleKeyDown = (e: KeyboardEvent) => {
    const keyCode = e.keyCode;

    // Handle special keys
    if (this.handleSpecialKeyDown(keyCode)) {
      e.preventDefault();
      return;
    }

    // Handle normal keys
    var key = this.keys[keyCode];
    if (key) {
      this.onButtonDown(key[0], key[1]);
      e.preventDefault();
    }
  };

  handleKeyUp = (e: KeyboardEvent) => {
    const keyCode = e.keyCode;

    // Handle special keys
    if (this.handleSpecialKeyUp(keyCode)) {
      e.preventDefault();
      return;
    }

    // Handle normal keys
    var key = this.keys[keyCode];
    if (key) {
      this.onButtonUp(key[0], key[1]);
      e.preventDefault();
    }
  };

  private handleSpecialKeyDown = (keyCode: number): boolean => {
    switch (keyCode) {
      case 76: // L key - A+B combo
        this.comboKeyStates[76] = true;
        this.onButtonDown(1, Controller.BUTTON_A);
        this.onButtonDown(1, Controller.BUTTON_B);
        return true;

      case 85: // U key - J rapid fire (B button)
        if (!this.rapidFireIntervals[85]) {
          this.startRapidFire(85, 1, Controller.BUTTON_B);
        }
        return true;

      case 73: // I key - K rapid fire (A button)
        if (!this.rapidFireIntervals[73]) {
          this.startRapidFire(73, 1, Controller.BUTTON_A);
        }
        return true;

      case 79: // O key - L rapid fire (A+B combo)
        if (!this.rapidFireIntervals[79]) {
          this.startRapidFireCombo(79);
        }
        return true;

      default:
        return false;
    }
  };

  private handleSpecialKeyUp = (keyCode: number): boolean => {
    switch (keyCode) {
      case 76: // L key - A+B combo
        this.comboKeyStates[76] = false;
        this.onButtonUp(1, Controller.BUTTON_A);
        this.onButtonUp(1, Controller.BUTTON_B);
        return true;

      case 85: // U key - stop J rapid fire
        this.stopRapidFire(85);
        return true;

      case 73: // I key - stop K rapid fire
        this.stopRapidFire(73);
        return true;

      case 79: // O key - stop L rapid fire
        this.stopRapidFire(79);
        return true;

      default:
        return false;
    }
  };

  private startRapidFire = (keyCode: number, controller: number, button: number) => {
    // Initial press
    this.onButtonDown(controller, button);

    // Set up rapid fire interval (10 times per second)
    this.rapidFireIntervals[keyCode] = setInterval(() => {
      this.onButtonUp(controller, button);
      setTimeout(() => {
        this.onButtonDown(controller, button);
      }, 25); // 25ms off, 75ms on = 10Hz
    }, 100);
  };

  private startRapidFireCombo = (keyCode: number) => {
    // Initial press
    this.onButtonDown(1, Controller.BUTTON_A);
    this.onButtonDown(1, Controller.BUTTON_B);

    // Set up rapid fire interval for combo
    this.rapidFireIntervals[keyCode] = setInterval(() => {
      this.onButtonUp(1, Controller.BUTTON_A);
      this.onButtonUp(1, Controller.BUTTON_B);
      setTimeout(() => {
        this.onButtonDown(1, Controller.BUTTON_A);
        this.onButtonDown(1, Controller.BUTTON_B);
      }, 25);
    }, 100);
  };

  private stopRapidFire = (keyCode: number) => {
    if (this.rapidFireIntervals[keyCode]) {
      clearInterval(this.rapidFireIntervals[keyCode]);
      delete this.rapidFireIntervals[keyCode];

      // Final button release
      if (keyCode === 85) { // U key
        this.onButtonUp(1, Controller.BUTTON_B);
      } else if (keyCode === 73) { // I key
        this.onButtonUp(1, Controller.BUTTON_A);
      } else if (keyCode === 79) { // O key
        this.onButtonUp(1, Controller.BUTTON_A);
        this.onButtonUp(1, Controller.BUTTON_B);
      }
    }
  };

  // Clean up method to clear all intervals
  cleanup = () => {
    Object.keys(this.rapidFireIntervals).forEach(keyCode => {
      this.stopRapidFire(parseInt(keyCode));
    });
  };

  handleKeyPress = (e: KeyboardEvent) => {
    e.preventDefault();
  };
}