'use client';

import React, { useState, useEffect } from 'react';
import { SaveSlot, SaveStateManager } from '@/utils/SaveStateManager';

interface SaveStateUIProps {
  onSave: () => SaveSlot | null;
  onLoad: (slotId: number) => boolean;
  onDelete: (slotId: number) => boolean;
  onClearAll: () => void;
  getSaveStates: () => SaveSlot[];
  isVisible: boolean;
  onClose: () => void;
}

export default function SaveStateUI({
  onSave,
  onLoad,
  onDelete,
  onClearAll,
  getSaveStates,
  isVisible,
  onClose
}: SaveStateUIProps) {
  const [saveStates, setSaveStates] = useState<SaveSlot[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<{ text: string; type: 'success' | 'error' } | null>(null);

  // 刷新存档列表
  const refreshSaveStates = () => {
    setSaveStates(getSaveStates());
  };

  // 组件挂载时刷新存档列表
  useEffect(() => {
    if (isVisible) {
      refreshSaveStates();
    }
  }, [isVisible]);

  // 显示消息
  const showMessage = (text: string, type: 'success' | 'error') => {
    setMessage({ text, type });
    setTimeout(() => setMessage(null), 3000);
  };

  // 处理保存
  const handleSave = async () => {
    setIsLoading(true);
    try {
      const saveSlot = onSave();
      if (saveSlot) {
        refreshSaveStates();
        showMessage(`存档已保存到位置 ${saveSlot.id}`, 'success');
      } else {
        showMessage('保存失败', 'error');
      }
    } catch (error) {
      console.error('Save error:', error);
      showMessage('保存失败', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // 处理加载
  const handleLoad = async (slotId: number) => {
    setIsLoading(true);
    try {
      const success = onLoad(slotId);
      if (success) {
        showMessage(`已加载存档 ${slotId}`, 'success');
        onClose(); // 加载成功后关闭UI
      } else {
        showMessage('加载失败', 'error');
      }
    } catch (error) {
      console.error('Load error:', error);
      showMessage('加载失败', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // 处理删除
  const handleDelete = (slotId: number) => {
    if (window.confirm('确定要删除这个存档吗？')) {
      const success = onDelete(slotId);
      if (success) {
        refreshSaveStates();
        showMessage(`存档 ${slotId} 已删除`, 'success');
      } else {
        showMessage('删除失败', 'error');
      }
    }
  };

  // 处理清空所有存档
  const handleClearAll = () => {
    if (window.confirm('确定要删除所有存档吗？此操作不可恢复！')) {
      onClearAll();
      refreshSaveStates();
      showMessage('所有存档已清空', 'success');
    }
  };

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4 max-h-[80vh] overflow-y-auto">
        {/* 标题栏 */}
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-gray-900 dark:text-white">游戏存档</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 消息提示 */}
        {message && (
          <div className={`mb-4 p-3 rounded ${
            message.type === 'success' 
              ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
              : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
          }`}>
            {message.text}
          </div>
        )}

        {/* 操作按钮 */}
        <div className="mb-4 space-y-2">
          <button
            onClick={handleSave}
            disabled={isLoading}
            className="w-full bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white font-medium py-2 px-4 rounded transition-colors"
          >
            {isLoading ? '保存中...' : '创建新存档'}
          </button>
          
          {saveStates.length > 0 && (
            <button
              onClick={handleClearAll}
              disabled={isLoading}
              className="w-full bg-red-500 hover:bg-red-600 disabled:bg-red-300 text-white font-medium py-2 px-4 rounded transition-colors"
            >
              清空所有存档
            </button>
          )}
        </div>

        {/* 存档列表 */}
        <div className="space-y-3">
          {saveStates.length === 0 ? (
            <div className="text-center text-gray-500 dark:text-gray-400 py-8">
              <svg className="w-12 h-12 mx-auto mb-2 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <p>暂无存档</p>
              <p className="text-sm">点击"创建新存档"开始</p>
            </div>
          ) : (
            saveStates.map((slot) => (
              <div key={slot.id} className="border border-gray-200 dark:border-gray-600 rounded-lg p-3">
                <div className="flex items-start space-x-3">
                  {/* 截图预览 */}
                  {slot.screenshot && (
                    <div className="flex-shrink-0">
                      <img
                        src={slot.screenshot}
                        alt={`存档 ${slot.id} 截图`}
                        className="w-16 h-12 object-cover rounded border"
                      />
                    </div>
                  )}
                  
                  {/* 存档信息 */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                        存档 {slot.id}
                      </h3>
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {SaveStateManager.formatTimestamp(slot.timestamp)}
                      </span>
                    </div>
                    
                    {/* 操作按钮 */}
                    <div className="mt-2 flex space-x-2">
                      <button
                        onClick={() => handleLoad(slot.id)}
                        disabled={isLoading}
                        className="text-xs bg-green-500 hover:bg-green-600 disabled:bg-green-300 text-white px-2 py-1 rounded transition-colors"
                      >
                        加载
                      </button>
                      <button
                        onClick={() => handleDelete(slot.id)}
                        disabled={isLoading}
                        className="text-xs bg-red-500 hover:bg-red-600 disabled:bg-red-300 text-white px-2 py-1 rounded transition-colors"
                      >
                        删除
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>

        {/* 快捷键提示 */}
        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
          <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
            快捷键：F5-F7 保存存档，F9-F11 加载存档
          </p>
        </div>
      </div>
    </div>
  );
}
