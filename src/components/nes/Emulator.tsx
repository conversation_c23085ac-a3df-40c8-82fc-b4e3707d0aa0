import React, { Component, forwardRef, useImper<PERSON><PERSON><PERSON><PERSON> } from "react";
import { NES, SaveState } from "jsnes";

import FrameTimer from "./FrameTimer";
import GamepadController from "./GamepadController";
import KeyboardController from "./KeyboardController";
import Screen from "./Screen";
import Speakers from "./Speakers";
import { SaveStateManager, SaveSlot } from "@/utils/SaveStateManager";

interface EmulatorProps {
  paused?: boolean;
  romData: string;
  onLoadingChange?: (loading: boolean) => void;
}

interface EmulatorState {
  isReady: boolean;
}

/*
 * Runs the emulator.
 *
 * The only UI is a canvas element. It assumes it is a singleton in various ways
 * (binds to window, keyboard, speakers, etc).
 */
class Emulator extends Component<EmulatorProps, EmulatorState> {
  private screen: Screen | null = null;
  private speakers!: Speakers;
  private nes!: NES;
  private frameTimer!: FrameTimer;
  private gamepadController!: GamepadController;
  private keyboardController!: KeyboardController;
  private gamepadPolling: { stop: () => void } | null = null;
  private fpsInterval: NodeJS.Timeout | null = null;
  private audioStarted: boolean = false;
  private saveStateManager!: SaveStateManager;

  constructor(props: EmulatorProps) {
    super(props);
    this.state = {
      isReady: false
    };
  }

  render() {
    return (
      <div className="w-full h-full flex items-center justify-center bg-black">
        <Screen
          ref={screen => {
            this.screen = screen;
          }}
          onMouseDown={(x, y) => {
            this.handleUserInteraction();
            if (this.nes) {
              this.nes.zapperMove(x, y);
              this.nes.zapperFireDown();
            }
          }}
          onMouseUp={() => {
            if (this.nes) {
              this.nes.zapperFireUp();
            }
          }}
        />
        
        {/* Controls help overlay */}
        <div className="absolute bottom-4 left-4 bg-black bg-opacity-70 text-white p-2 rounded text-xs opacity-0 hover:opacity-100 transition-opacity">
          <p className="font-semibold mb-1">Controls:</p>
          <p>WASD: D-pad</p>
          <p>J: B button, K: A button, L: A+B</p>
          <p>U: B rapid, I: A rapid, O: A+B rapid</p>
          <p>Enter: Start, Ctrl: Select</p>
        </div>
      </div>
    );
  }

  componentDidMount() {
    this.initializeEmulator();
  }

  private handleUserInteraction = async () => {
    if (!this.audioStarted && this.speakers) {
      try {
        await this.speakers.start();
        this.audioStarted = true;
        console.log('Audio started successfully');
      } catch (error) {
        console.error('Failed to start audio:', error);
      }
    }
  };

  componentWillUnmount() {
    this.stop();

    // Unbind keyboard
    if (this.keyboardController) {
      this.keyboardController.cleanup(); // Clean up rapid fire intervals
      document.removeEventListener("keydown", this.keyboardController.handleKeyDown);
      document.removeEventListener("keyup", this.keyboardController.handleKeyUp);
      document.removeEventListener("keypress", this.keyboardController.handleKeyPress);
    }

    // Stop gamepad
    if (this.gamepadPolling) {
      this.gamepadPolling.stop();
    }

    // Clean up global reference
    (window as any)["nes"] = undefined;
  }

  componentDidUpdate(prevProps: EmulatorProps) {
    if (this.props.paused !== prevProps.paused) {
      if (this.props.paused) {
        this.stop();
      } else {
        this.start();
      }
    }

    // Handle ROM change
    if (this.props.romData !== prevProps.romData) {
      this.loadROM();
    }
  }

  private async initializeEmulator() {
    try {
      this.props.onLoadingChange?.(true);

      // 初始化存档管理器
      const gameId = SaveStateManager.generateGameId(this.props.romData);
      this.saveStateManager = new SaveStateManager(gameId);

      this.speakers = new Speakers({
        onBufferUnderrun: (_actualSize, desiredSize) => {
          if (this.props.paused) {
            return;
          }
          // Skip a video frame so audio remains consistent. This happens for
          // a variety of reasons:
          // - Frame rate is not quite 60fps, so sometimes buffer empties
          // - Page is not visible, so requestAnimationFrame doesn't get fired.
          //   In this case emulator still runs at full speed, but timing is
          //   done by audio instead of requestAnimationFrame.
          // - System can't run emulator at full speed. In this case it'll stop
          //    firing requestAnimationFrame.
          console.log("Buffer underrun, running another frame to try and catch up");

          this.frameTimer.generateFrame();
          // desiredSize will be 2048, and the NES produces 1468 samples on each
          // frame so we might need a second frame to be run. Give up after that
          // though -- the system is not catching up
          if (this.speakers.buffer.size() < desiredSize) {
            console.log("Still buffer underrun, running a second frame");
            this.frameTimer.generateFrame();
          }
        }
      });

      this.nes = new NES({
        onFrame: (frameBuffer: number[]) => {
          if (this.screen) {
            this.screen.setBuffer(frameBuffer);
          }
        },
        onStatusUpdate: console.log,
        onAudioSample: this.speakers.writeSample,
        sampleRate: this.speakers.getSampleRate()
      });

      // For debugging
      (window as any)["nes"] = this.nes;

      this.frameTimer = new FrameTimer({
        onGenerateFrame: () => this.nes.frame(),
        onWriteFrame: () => {
          if (this.screen) {
            this.screen.writeBuffer();
          }
        }
      });

      // Set up gamepad and keyboard
      this.gamepadController = new GamepadController({
        onButtonDown: (controller, button) => this.nes.buttonDown(controller, button),
        onButtonUp: (controller, button) => this.nes.buttonUp(controller, button)
      });

      this.gamepadController.loadGamepadConfig();
      this.gamepadPolling = this.gamepadController.startPolling();

      this.keyboardController = new KeyboardController({
        onButtonDown: this.gamepadController.disableIfGamepadEnabled(
          (controller, button) => {
            this.handleUserInteraction(); // Start audio on key press (non-blocking)
            this.nes.buttonDown(controller, button);
          }
        ),
        onButtonUp: this.gamepadController.disableIfGamepadEnabled(
          (controller, button) => this.nes.buttonUp(controller, button)
        )
      });

      // Load keys from localStorage (if they exist)
      this.keyboardController.loadKeys();

      document.addEventListener("keydown", this.keyboardController.handleKeyDown);
      document.addEventListener("keyup", this.keyboardController.handleKeyUp);
      document.addEventListener("keypress", this.keyboardController.handleKeyPress);

      // Load ROM
      await this.loadROM();
      
      this.start();
    } catch (error) {
      console.error('Failed to initialize emulator:', error);
      this.props.onLoadingChange?.(false);
    }
  }

  private async loadROM() {
    try {
      this.props.onLoadingChange?.(true);
      
      // Fetch ROM data
      const response = await fetch(this.props.romData);
      if (!response.ok) {
        throw new Error(`Failed to load ROM: ${response.statusText}`);
      }
      
      const arrayBuffer = await response.arrayBuffer();
      const romData = new Uint8Array(arrayBuffer);
      
      // Convert to binary string as expected by jsnes
      let binaryString = '';
      for (let i = 0; i < romData.length; i++) {
        binaryString += String.fromCharCode(romData[i]);
      }
      
      this.nes.loadROM(binaryString);
      this.props.onLoadingChange?.(false);

      // 标记模拟器已准备好
      this.setState({ isReady: true });
      console.log('NES emulator is now ready for save states');
    } catch (error) {
      console.error('Failed to load ROM:', error);
      this.props.onLoadingChange?.(false);
      throw error;
    }
  }

  start = () => {
    if (this.frameTimer) {
      this.frameTimer.start();
      // Audio will be started on user interaction
      this.fpsInterval = setInterval(() => {
        if (this.nes && typeof this.nes.getFPS === 'function') {
          // console.log(`FPS: ${this.nes.getFPS()}`);
        }
      }, 1000);
    }
  };

  stop = () => {
    if (this.frameTimer) {
      this.frameTimer.stop();
    }
    if (this.speakers && this.audioStarted) {
      this.speakers.stop();
      this.audioStarted = false;
    }
    if (this.fpsInterval) {
      clearInterval(this.fpsInterval);
      this.fpsInterval = null;
    }
  };

  /**
   * 保存游戏状态
   */
  saveState = (): SaveSlot | null => {
    console.log('saveState called');
    console.log('isReady:', this.state.isReady);
    console.log('this.nes:', !!this.nes);
    console.log('this.saveStateManager:', !!this.saveStateManager);

    if (!this.state.isReady) {
      console.error('NES emulator not ready yet - game may still be loading');
      return null;
    }

    if (!this.nes) {
      console.error('NES emulator not initialized');
      return null;
    }

    if (!this.saveStateManager) {
      console.error('Save state manager not initialized');
      return null;
    }

    try {
      // 获取当前游戏状态
      console.log('Getting NES state...');
      const nesState = this.nes.toJSON();
      console.log('NES state obtained:', !!nesState);

      if (!nesState) {
        console.error('Failed to get NES state - toJSON returned null/undefined');
        return null;
      }

      // 分析状态数据大小
      const stateString = JSON.stringify(nesState);
      const stateSize = new Blob([stateString]).size;
      console.log(`NES state size: ${SaveStateManager.formatSize(stateSize)}`);
      console.log('NES state keys:', Object.keys(nesState));

      // 分析各个组件的大小
      Object.keys(nesState).forEach(key => {
        const componentString = JSON.stringify(nesState[key]);
        const componentSize = new Blob([componentString]).size;
        console.log(`  ${key}: ${SaveStateManager.formatSize(componentSize)}`);
      });

      // 获取屏幕截图
      console.log('Getting screenshot...');
      const screenshot = this.screen?.getScreenshot();
      console.log('Screenshot obtained:', !!screenshot);
      if (screenshot) {
        const screenshotSize = new Blob([screenshot]).size;
        console.log(`Screenshot size: ${SaveStateManager.formatSize(screenshotSize)}`);
      }

      // 保存状态
      console.log('Saving state to manager...');
      const saveSlot = this.saveStateManager.saveState(nesState, screenshot);
      console.log('Save slot created:', saveSlot);

      return saveSlot;
    } catch (error) {
      console.error('Failed to save state:', error);
      if (error instanceof Error) {
        console.error('Error message:', error.message);
        console.error('Error stack:', error.stack);
      }
      return null;
    }
  };

  /**
   * 加载游戏状态
   */
  loadState = (slotId: number): boolean => {
    if (!this.nes || !this.saveStateManager) {
      console.error('NES emulator or save state manager not initialized');
      return false;
    }

    try {
      const saveSlot = this.saveStateManager.loadState(slotId);
      if (!saveSlot) {
        console.error(`Save slot ${slotId} not found`);
        return false;
      }

      // 恢复游戏状态
      this.nes.fromJSON(saveSlot.data);

      console.log(`Game state loaded from slot ${slotId}`);
      return true;
    } catch (error) {
      console.error('Failed to load state:', error);
      return false;
    }
  };

  /**
   * 获取所有存档
   */
  getSaveStates = (): SaveSlot[] => {
    if (!this.saveStateManager) {
      return [];
    }
    return this.saveStateManager.getSaveStates();
  };

  /**
   * 删除存档
   */
  deleteState = (slotId: number): boolean => {
    if (!this.saveStateManager) {
      return false;
    }

    return this.saveStateManager.deleteState(slotId);
  };

  /**
   * 清空所有存档
   */
  clearAllStates = (): void => {
    if (!this.saveStateManager) {
      return;
    }

    this.saveStateManager.clearAllStates();
  };

  /**
   * 检查是否有存档
   */
  hasStates = (): boolean => {
    if (!this.saveStateManager) {
      return false;
    }
    return this.saveStateManager.hasStates();
  };

  /**
   * 检查模拟器是否准备好进行存档操作
   */
  isReady = (): boolean => {
    return this.state.isReady && !!this.nes && !!this.saveStateManager;
  };

  /**
   * 获取存储信息
   */
  getStorageInfo = (): string => {
    if (!this.saveStateManager) {
      return 'Storage info unavailable';
    }
    return this.saveStateManager.getStorageInfo();
  };

}

export default Emulator;