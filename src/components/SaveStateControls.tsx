'use client';

import React, { useState, useEffect } from 'react';
import { SaveSlot } from '@/utils/SaveStateManager';

interface SaveStateControlsProps {
  onSave: () => SaveSlot | null;
  onLoadLatest: () => boolean;
  onClearAll: () => void;
  getSaveStates: () => SaveSlot[];
  onLoadSpecific: (slotId: number) => boolean;
  onDeleteSpecific: (slotId: number) => boolean;
  isReady: () => boolean;
}

export default function SaveStateControls({
  onSave,
  onLoadLatest,
  onClearAll,
  getSaveStates,
  onLoadSpecific,
  onDeleteSpecific,
  isReady
}: SaveStateControlsProps) {
  const [saveStates, setSaveStates] = useState<SaveSlot[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<{ text: string; type: 'success' | 'error' } | null>(null);
  const [showAllStates, setShowAllStates] = useState(false);
  const [emulatorReady, setEmulatorReady] = useState(false);

  // Refresh save states and check emulator readiness
  const refreshSaveStates = () => {
    setSaveStates(getSaveStates());
    setEmulatorReady(isReady());
  };

  // Refresh on mount and periodically
  useEffect(() => {
    refreshSaveStates();
    const interval = setInterval(refreshSaveStates, 500); // Check more frequently
    return () => clearInterval(interval);
  }, []);

  // Show message
  const showMessage = (text: string, type: 'success' | 'error') => {
    setMessage({ text, type });
    setTimeout(() => setMessage(null), 3000);
  };

  // Handle save
  const handleSave = async () => {
    if (!emulatorReady) {
      showMessage('Game is still loading, please wait...', 'error');
      return;
    }

    setIsLoading(true);
    try {
      console.log('Attempting to save game state...');
      const saveSlot = onSave();
      console.log('Save result:', saveSlot);

      if (saveSlot) {
        refreshSaveStates();
        showMessage(`Game saved successfully!`, 'success');
      } else {
        console.error('Save failed: onSave returned null');
        showMessage('Save failed - Please try again', 'error');
      }
    } catch (error) {
      console.error('Save error:', error);
      showMessage(`Save failed: ${error instanceof Error ? error.message : 'Unknown error'}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle load latest
  const handleLoadLatest = async () => {
    if (saveStates.length === 0) {
      showMessage('No saves available', 'error');
      return;
    }

    setIsLoading(true);
    try {
      const success = onLoadLatest();
      if (success) {
        showMessage('Game loaded successfully!', 'success');
      } else {
        showMessage('Load failed', 'error');
      }
    } catch (error) {
      console.error('Load error:', error);
      showMessage('Load failed', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle clear all
  const handleClearAll = () => {
    if (window.confirm('Are you sure you want to delete all save states? This cannot be undone!')) {
      onClearAll();
      refreshSaveStates();
      showMessage('All saves cleared', 'success');
    }
  };

  // Handle load specific
  const handleLoadSpecific = (slotId: number) => {
    setIsLoading(true);
    try {
      const success = onLoadSpecific(slotId);
      if (success) {
        showMessage('Game loaded successfully!', 'success');
      } else {
        showMessage('Load failed', 'error');
      }
    } catch (error) {
      console.error('Load error:', error);
      showMessage('Load failed', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle delete specific
  const handleDeleteSpecific = (slotId: number) => {
    if (window.confirm('Are you sure you want to delete this save state?')) {
      const success = onDeleteSpecific(slotId);
      if (success) {
        refreshSaveStates();
        showMessage('Save state deleted', 'success');
      } else {
        showMessage('Delete failed', 'error');
      }
    }
  };

  const latestSave = saveStates.length > 0 ? saveStates[0] : null;

  return (
    <div className="mt-4 sm:mt-6 p-4 sm:p-6 bg-purple-50 dark:bg-purple-900/30 border border-purple-200 dark:border-purple-800 rounded-lg">
      <div className="flex items-center gap-2 mb-4">
        <span className="text-xl" role="img" aria-hidden="true">💾</span>
        <h3 className="text-base sm:text-lg font-semibold text-purple-900 dark:text-purple-200">
          Save States
        </h3>
      </div>

      {/* Message */}
      {message && (
        <div className={`mb-4 p-3 rounded-lg text-sm ${
          message.type === 'success' 
            ? 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200'
            : 'bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-200'
        }`}>
          {message.text}
        </div>
      )}

      {/* Main Controls */}
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 mb-4">
        <button
          onClick={handleSave}
          disabled={isLoading || !emulatorReady}
          className="flex items-center justify-center gap-2 px-4 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium rounded-lg transition-colors"
          title={!emulatorReady ? 'Game is still loading...' : 'Save current game state'}
        >
          <span>💾</span>
          <div className="text-left">
            <div className="text-sm font-medium">
              {!emulatorReady ? 'Loading...' : 'Save Game'}
            </div>
            <div className="text-xs opacity-90">Ctrl+W</div>
          </div>
        </button>

        <button
          onClick={handleLoadLatest}
          disabled={isLoading || saveStates.length === 0}
          className="flex items-center justify-center gap-2 px-4 py-3 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-medium rounded-lg transition-colors"
        >
          <span>📂</span>
          <div className="text-left">
            <div className="text-sm font-medium">Load Latest</div>
            <div className="text-xs opacity-90">Ctrl+R</div>
          </div>
        </button>

        <button
          onClick={handleClearAll}
          disabled={isLoading || saveStates.length === 0}
          className="flex items-center justify-center gap-2 px-4 py-3 bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white font-medium rounded-lg transition-colors"
        >
          <span>🗑️</span>
          <div className="text-left">
            <div className="text-sm font-medium">Clear All</div>
            <div className="text-xs opacity-90">{saveStates.length} saves</div>
          </div>
        </button>
      </div>

      {/* Latest Save Info */}
      {latestSave && (
        <div className="mb-4 p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {latestSave.screenshot && (
                <img
                  src={latestSave.screenshot}
                  alt="Latest save preview"
                  className="w-12 h-9 object-cover rounded border"
                />
              )}
              <div>
                <div className="text-sm font-medium text-gray-900 dark:text-white">
                  Latest Save
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  {new Date(latestSave.timestamp).toLocaleString()}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* All Save States (Expandable) */}
      {saveStates.length > 1 && (
        <div>
          <button
            onClick={() => setShowAllStates(!showAllStates)}
            className="w-full text-left text-sm text-purple-700 dark:text-purple-300 hover:text-purple-900 dark:hover:text-purple-100 mb-3"
          >
            {showAllStates ? '▼' : '▶'} Show all save states ({saveStates.length})
          </button>

          {showAllStates && (
            <div className="space-y-2">
              {saveStates.map((save, index) => (
                <div key={save.id} className="flex items-center justify-between p-3 bg-white dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-600">
                  <div className="flex items-center gap-3">
                    {save.screenshot && (
                      <img
                        src={save.screenshot}
                        alt={`Save ${save.id} preview`}
                        className="w-10 h-8 object-cover rounded border"
                      />
                    )}
                    <div>
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        Save {save.id} {index === 0 && '(Latest)'}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {new Date(save.timestamp).toLocaleString()}
                      </div>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <button
                      onClick={() => handleLoadSpecific(save.id)}
                      disabled={isLoading}
                      className="px-3 py-1 text-xs bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white rounded"
                    >
                      Load
                    </button>
                    <button
                      onClick={() => handleDeleteSpecific(save.id)}
                      disabled={isLoading}
                      className="px-3 py-1 text-xs bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white rounded"
                    >
                      Delete
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* No saves message */}
      {saveStates.length === 0 && (
        <div className="text-center py-4 text-gray-500 dark:text-gray-400">
          <div className="text-2xl mb-2">🎮</div>
          <div className="text-sm">No save states yet</div>
          <div className="text-xs">Click "Save Game" to create your first save</div>
        </div>
      )}
    </div>
  );
}
