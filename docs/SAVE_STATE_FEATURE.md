# NES游戏存档功能说明

## 功能概述

为NES游戏开发了完整的存档功能，支持3个存档点，最新的存档会自动覆盖最旧的存档。

## 主要特性

- ✅ **3个存档位置**：支持最多3个存档点
- ✅ **自动覆盖**：当存档超过3个时，自动删除最旧的存档
- ✅ **截图预览**：每个存档都包含游戏画面截图
- ✅ **时间戳记录**：显示存档创建时间
- ✅ **键盘快捷键**：支持快速保存和加载
- ✅ **本地存储**：使用localStorage保存，无需服务器
- ✅ **游戏隔离**：不同游戏的存档互不干扰

## 使用方法

### 图形界面操作

1. **打开存档管理**：
   - 点击游戏界面右上角的存档按钮（💾图标）
   - 或按F8键

2. **创建存档**：
   - 在存档管理界面点击"创建新存档"按钮
   - 存档会自动包含当前游戏状态和截图

3. **加载存档**：
   - 在存档列表中点击对应存档的"加载"按钮
   - 游戏会立即恢复到存档时的状态

4. **删除存档**：
   - 点击存档的"删除"按钮
   - 确认后存档将被永久删除

5. **清空所有存档**：
   - 点击"清空所有存档"按钮
   - 确认后所有存档将被删除

### 键盘快捷键

| 按键 | 功能 |
|------|------|
| F5-F7 | 快速保存存档 |
| F9-F11 | 快速加载存档（如果存在） |
| F8 | 打开存档管理界面 |

## 技术实现

### 核心组件

1. **SaveStateManager** (`src/utils/SaveStateManager.ts`)
   - 存档数据管理
   - localStorage操作
   - 存档生命周期管理

2. **SaveStateUI** (`src/components/nes/SaveStateUI.tsx`)
   - 存档管理界面
   - 用户交互处理
   - 存档列表显示

3. **Emulator扩展** (`src/components/nes/Emulator.tsx`)
   - 集成jsnes的toJSON/fromJSON方法
   - 截图功能
   - 存档状态通知

4. **GameContainer集成** (`src/components/GameContainer.tsx`)
   - 键盘快捷键处理
   - UI集成
   - 仅NES游戏显示

### 数据结构

```typescript
interface SaveSlot {
  id: number;              // 存档ID
  timestamp: number;       // 创建时间戳
  gameId: string;         // 游戏标识
  data: SaveState;        // 游戏状态数据
  screenshot?: string;    // 截图（base64）
}
```

### 存储机制

- **存储位置**：浏览器localStorage
- **存储键**：`nes_save_{gameId}`
- **游戏ID生成**：基于ROM URL自动生成
- **最大存档数**：3个（可配置）
- **覆盖策略**：FIFO（先进先出）

## 兼容性

- ✅ 支持所有NES游戏
- ✅ 兼容现有的jsnes模拟器
- ✅ 不影响游戏性能
- ✅ 支持全屏模式
- ✅ 响应式设计

## 使用限制

1. **浏览器支持**：需要支持localStorage的现代浏览器
2. **存储空间**：受浏览器localStorage限制（通常5-10MB）
3. **游戏状态**：只能保存jsnes支持的游戏状态
4. **跨设备**：存档仅保存在本地，不支持云同步

## 故障排除

### 常见问题

1. **存档按钮不显示**
   - 确保当前游戏是NES游戏
   - 检查游戏是否已完全加载

2. **快捷键不工作**
   - 确保焦点在游戏区域
   - 检查是否在输入框中

3. **存档失败**
   - 检查浏览器localStorage是否可用
   - 确认存储空间是否充足

4. **加载失败**
   - 确认存档文件完整性
   - 检查游戏版本是否匹配

### 调试方法

1. **浏览器控制台**：
   ```javascript
   // 查看存档数据
   localStorage.getItem('nes_save_super_mario_bros')
   
   // 清除特定游戏存档
   localStorage.removeItem('nes_save_super_mario_bros')
   ```

2. **测试脚本**：
   ```javascript
   // 运行测试
   import('./test/savestate-test.js').then(m => m.testSaveStateManager())
   ```

## 未来改进

- [ ] 云存储同步
- [ ] 存档导入/导出
- [ ] 存档备注功能
- [ ] 更多存档位置
- [ ] 存档压缩优化
- [ ] 存档分享功能

## 开发者说明

如需修改存档功能，主要文件位置：

- 核心逻辑：`src/utils/SaveStateManager.ts`
- UI组件：`src/components/nes/SaveStateUI.tsx`
- 模拟器集成：`src/components/nes/Emulator.tsx`
- 容器集成：`src/components/GameContainer.tsx`
- 类型定义：`src/types/jsnes.d.ts`
